// Copyright iYuuki Studio

#pragma once

#include "CoreMinimal.h"
#include "AbilitySystem/Data/AuraCharacterClassInfo.h"
#include "Character/AuraCharacterBase.h"
#include "Interaction/EnemyInterface.h"
#include "Enemy.generated.h"

struct FGameplayTag;
/**
 * 
 */
UCLASS()
class AURA_API AEnemy : public AAuraCharacterBase, public IEnemyInterface
{
	GENERATED_BODY()

public:
	AEnemy();

	/** EnemyInterface */
	virtual void HighLight() override;
	virtual void UnHighlight() override;
	virtual void Die() override;
	/** end EnemyInterface */

	/** CombatInterface */
	virtual int32 GetCharacterLevel() override;
	/** end CombatInterface */

	UPROPERTY(BlueprintReadOnly, Category = "Combat")
	bool bHitReacting = false;
	UPROPERTY(EditAnywhere, Category = "Character Defaults")
	float BaseWalkSpeed = 250.f;
	
	void HitReactTagChanged(const FGameplayTag Tag, int32 NewCount);
protected:
	virtual void BeginPlay() override;
	virtual void InitAbilityActorInfo() override;
	virtual void InitialAttributes() override;

private:
	UPROPERTY(EditAnywhere, Category = "Character Defaults")
	ECharacterClass CharacterClass = ECharacterClass::Warrior;

	UPROPERTY(EditAnywhere, Category = "Character Defaults")
	float LifeSpan = 5.f;
};
