// Copyright iYuuki Studio

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/HUD.h"
#include "AuraHUD.generated.h"

class UAuraUserWidget;
/**
 * 
 */
UCLASS()
class AURA_API AAuraHUD : public AHUD
{
	GENERATED_BODY()

public:
	UPROPERTY()
	TObjectPtr<UAuraUserWidget> AuraOverlay;

protected:
	virtual void BeginPlay() override;

private:
	UPROPERTY(EditAnywhere, Category ="HUD")
	TSubclassOf<UAuraUserWidget> AuraWidgetClass;
};
