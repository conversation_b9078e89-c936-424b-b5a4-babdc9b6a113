// Copyright iYuuki Studio


#include "AbilitySystem/MMC/MMC_MaxMana.h"

#include "AbilitySystem/AuraAttributeSet.h"
#include "Interaction/CombatInterface.h"

UMMC_MaxMana::UMMC_MaxMana()
{
	IntelligenceCapturedDef.AttributeToCapture = UAuraAttributeSet::GetIntelligenceAttribute();
	IntelligenceCapturedDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
	IntelligenceCapturedDef.bSnapshot = false;

	RelevantAttributesToCapture.Add(IntelligenceCapturedDef);
}

float UMMC_MaxMana::CalculateBaseMagnitude_Implementation(const FGameplayEffectSpec& Spec) const
{
	const FGameplayTagContainer* TargetTags = Spec.CapturedTargetTags.GetAggregatedTags();
	const FGameplayTagContainer* SourceTags = Spec.CapturedSourceTags.GetAggregatedTags();

	FAggregatorEvaluateParameters EvaluationParameters;
	EvaluationParameters.SourceTags = SourceTags;
	EvaluationParameters.TargetTags = TargetTags;

	float OutIntelligence = 0.f;
	GetCapturedAttributeMagnitude(IntelligenceCapturedDef, Spec, EvaluationParameters, OutIntelligence);
	OutIntelligence = FMath::Max(OutIntelligence, 0.f);

	float BaseValue = 50.f + 2 * OutIntelligence;

	ICombatInterface* CombatInterface = Cast<ICombatInterface>(Spec.GetContext().GetSourceObject());
	if (CombatInterface)
	{
		const int32 PlayerLevel = CombatInterface->GetCharacterLevel();
		return BaseValue + 1.5f * PlayerLevel;
	}

	return BaseValue;
}
