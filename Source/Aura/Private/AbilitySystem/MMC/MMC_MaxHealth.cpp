// Copyright iYuuki Studio


#include "AbilitySystem/MMC/MMC_MaxHealth.h"

#include "AbilitySystem/AuraAttributeSet.h"
#include "Interaction/CombatInterface.h"

UMMC_MaxHealth::UMMC_MaxHealth()
{
	VigorCaptureDef.AttributeToCapture = UAuraAttributeSet::GetVigorAttribute();
	VigorCaptureDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
	VigorCaptureDef.bSnapshot = false;

	RelevantAttributesToCapture.Add(VigorCaptureDef);
}

float UMMC_MaxHealth::CalculateBaseMagnitude_Implementation(const FGameplayEffectSpec& Spec) const
{
	const FGameplayTagContainer* TargetTags = Spec.CapturedTargetTags.GetAggregatedTags();
	const FGameplayTagContainer* SourceTags = Spec.CapturedSourceTags.GetAggregatedTags();

	FAggregatorEvaluateParameters EvaluationParameters;
	EvaluationParameters.SourceTags = SourceTags;
	EvaluationParameters.TargetTags = TargetTags;

	float OutVigor = 0.f;
	GetCapturedAttributeMagnitude(VigorCaptureDef, Spec, EvaluationParameters, OutVigor);
	OutVigor = FMath::Max(OutVigor, 0.f);

	float BaseValue = 80.f + 2 * OutVigor;

	ICombatInterface* CombatInterface = Cast<ICombatInterface>(Spec.GetContext().GetSourceObject());
	if (CombatInterface)
	{
		const int32 PlayerLevel = CombatInterface->GetCharacterLevel();
		return BaseValue + 2.5f * PlayerLevel;
	}
	 
	return BaseValue;
}
